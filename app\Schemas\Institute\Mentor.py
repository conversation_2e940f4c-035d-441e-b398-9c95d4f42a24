from pydantic import BaseModel, Field, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum


class MentorVerificationStatusEnum(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    UNDER_REVIEW = "under_review"


class AssociationStatusEnum(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    ACTIVE = "active"
    INACTIVE = "inactive"


class AssociationTypeEnum(str, Enum):
    MENTOR_APPLIED = "mentor_applied"
    INSTITUTE_INVITED = "institute_invited"


# Base Schemas
class MentorRegistrationBase(BaseModel):
    # User account details
    username: str = Field(..., min_length=3, max_length=50, description="Unique username")
    email: EmailStr = Field(..., description="Mentor email address")
    mobile: str = Field(..., description="Mentor contact number")
    password: str = Field(..., min_length=8, description="Account password")
    country: str = Field(..., description="Country")
    
    # Mentor profile details
    full_name: str = Field(..., min_length=2, max_length=200, description="Full name")
    bio: Optional[str] = Field(None, max_length=2000, description="Professional bio")
    expertise_areas: List[str] = Field(..., description="Areas of expertise")
    experience_years: Optional[int] = Field(None, ge=0, le=50, description="Years of experience")
    education: Optional[str] = Field(None, description="Educational background")
    certifications: Optional[List[str]] = Field(None, description="Professional certifications")
    
    # Contact details
    phone: Optional[str] = Field(None, description="Phone number")
    linkedin_url: Optional[str] = Field(None, description="LinkedIn profile URL")
    website: Optional[str] = Field(None, description="Personal website URL")
    
    # Professional details
    current_position: Optional[str] = Field(None, description="Current job position")
    current_organization: Optional[str] = Field(None, description="Current organization")
    languages: Optional[List[str]] = Field(None, description="Languages spoken")
    preferred_subjects: Optional[List[str]] = Field(None, description="Preferred subjects to mentor")
    hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Hourly rate in USD")

    @validator('username')
    def validate_username(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v.lower()

    @validator('linkedin_url', 'website')
    def validate_urls(cls, v):
        if v and not v.startswith(('http://', 'https://')):
            return f'https://{v}'
        return v


class MentorProfileUpdate(BaseModel):
    full_name: Optional[str] = Field(None, min_length=2, max_length=200)
    bio: Optional[str] = Field(None, max_length=2000)
    expertise_areas: Optional[List[str]] = None
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    education: Optional[str] = None
    certifications: Optional[List[str]] = None
    phone: Optional[str] = None
    linkedin_url: Optional[str] = None
    website: Optional[str] = None
    current_position: Optional[str] = None
    current_organization: Optional[str] = None
    languages: Optional[List[str]] = None
    preferred_subjects: Optional[List[str]] = None
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    availability_hours: Optional[Dict[str, Any]] = None
    profile_image_url: Optional[str] = None
    resume_url: Optional[str] = None
    portfolio_url: Optional[str] = None


class MentorVerificationUpdate(BaseModel):
    verification_status: MentorVerificationStatusEnum = Field(..., description="New verification status")
    verification_notes: Optional[str] = Field(None, max_length=1000, description="Admin notes")


# Institute-Mentor Association Schemas
class MentorApplicationCreate(BaseModel):
    institute_id: UUID = Field(..., description="Institute to apply to")
    application_message: str = Field(..., min_length=10, max_length=1000, description="Application message")
    proposed_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Proposed hourly rate")


class InstituteInvitationCreate(BaseModel):
    mentor_id: UUID = Field(..., description="Mentor to invite")
    invitation_message: str = Field(..., min_length=10, max_length=1000, description="Invitation message")
    offered_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Offered hourly rate")
    special_terms: Optional[str] = Field(None, max_length=500, description="Special terms")


class AssociationResponseCreate(BaseModel):
    response_message: str = Field(..., min_length=10, max_length=1000, description="Response message")
    accept: bool = Field(..., description="Accept or reject the application/invitation")
    negotiated_rate: Optional[Decimal] = Field(None, ge=0, description="Negotiated hourly rate")
    contract_terms: Optional[str] = Field(None, max_length=1000, description="Contract terms")


# Output Schemas
class MentorProfileOut(BaseModel):
    id: UUID
    user_id: UUID
    full_name: str
    bio: Optional[str]
    expertise_areas: Optional[List[str]]
    experience_years: Optional[int]
    education: Optional[str]
    certifications: Optional[List[str]]
    phone: Optional[str]
    linkedin_url: Optional[str]
    website: Optional[str]
    current_position: Optional[str]
    current_organization: Optional[str]
    languages: Optional[List[str]]
    preferred_subjects: Optional[List[str]]
    hourly_rate: Optional[Decimal]
    availability_hours: Optional[Dict[str, Any]]
    is_verified: bool
    verification_status: str
    verification_notes: Optional[str]
    verified_at: Optional[datetime]
    profile_image_url: Optional[str]
    resume_url: Optional[str]
    portfolio_url: Optional[str]
    rating: Optional[Decimal]
    total_reviews: int
    competitions_checked: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MentorUserOut(BaseModel):
    id: UUID
    username: str
    email: str
    mobile: str
    country: str
    profile_picture: Optional[str]
    user_type: str
    is_email_verified: bool
    is_mobile_verified: bool
    created_at: datetime
    mentor_profile: Optional[MentorProfileOut]

    class Config:
        from_attributes = True


class MentorDetailedOut(BaseModel):
    user: MentorUserOut
    profile: MentorProfileOut
    total_competitions: int = 0
    active_institutes: int = 0
    average_rating: Optional[Decimal] = None
    verification_status: str

    class Config:
        from_attributes = True


class MentorListOut(BaseModel):
    id: UUID
    username: str
    full_name: str
    expertise_areas: Optional[List[str]]
    experience_years: Optional[int]
    current_position: Optional[str]
    hourly_rate: Optional[Decimal]
    rating: Optional[Decimal]
    is_verified: bool
    verification_status: str
    profile_image_url: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True


class MentorListResponse(BaseModel):
    mentors: List[MentorListOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


# Association Output Schemas
class MentorInstituteAssociationOut(BaseModel):
    id: UUID
    mentor_id: UUID
    institute_id: UUID
    status: str
    association_type: str
    application_message: Optional[str]
    response_message: Optional[str]
    applied_at: datetime
    responded_at: Optional[datetime]
    approved_at: Optional[datetime]
    hourly_rate: Optional[Decimal]
    contract_terms: Optional[str]
    start_date: Optional[datetime]
    end_date: Optional[datetime]

    class Config:
        from_attributes = True


class MentorInstituteAssociationDetailedOut(BaseModel):
    association: MentorInstituteAssociationOut
    mentor: MentorListOut
    institute: Optional[Dict[str, Any]] = None  # Institute details as dict to avoid circular import

    class Config:
        from_attributes = True


# Search and filter schemas
class MentorSearchFilter(BaseModel):
    search: Optional[str] = None
    expertise_areas: Optional[List[str]] = None
    experience_years_min: Optional[int] = None
    experience_years_max: Optional[int] = None
    hourly_rate_min: Optional[Decimal] = None
    hourly_rate_max: Optional[Decimal] = None
    rating_min: Optional[Decimal] = None
    verification_status: Optional[MentorVerificationStatusEnum] = None
    is_verified: Optional[bool] = None
    country: Optional[str] = None
    languages: Optional[List[str]] = None
    preferred_subjects: Optional[List[str]] = None


class AssociationSearchFilter(BaseModel):
    status: Optional[AssociationStatusEnum] = None
    association_type: Optional[AssociationTypeEnum] = None
    institute_id: Optional[UUID] = None
    mentor_id: Optional[UUID] = None


# Statistics schemas
class MentorStatsOut(BaseModel):
    total_mentors: int
    verified_mentors: int
    pending_verification: int
    active_associations: int
    mentors_by_expertise: Dict[str, int]
    average_hourly_rate: Optional[Decimal]
    recent_registrations: int  # Last 30 days


class InstituteAssociationStatsOut(BaseModel):
    total_associations: int
    pending_applications: int
    active_mentors: int
    average_mentor_rating: Optional[Decimal]
    total_competitions_checked: int
