# EduFair API Documentation - Complete Institute Management System

## 🔐 **Authentication**
All protected endpoints require a Bearer token:
```http
Authorization: Bearer <your_jwt_token>
```

---

# 🏫 **INSTITUTE API REQUIREMENTS FOR EFFECTIVE UX**

## Overview
This document outlines ALL API endpoints required for Institute management system focused on their core responsibilities: **Managing Mentors** and **Creating Events**. Institutes serve as platforms that connect mentors with the educational ecosystem and organize events.

## Authentication
All endpoints require institute-level authentication:
```http
Authorization: Bearer <jwt_token>
```

---

## 🏫 **1. INSTITUTE DASHBOARD & ANALYTICS**

### **Dashboard Overview**
```http
GET /api/institute/dashboard/summary
GET /api/institute/dashboard/analytics
GET /api/institute/dashboard/recent-activities
GET /api/institute/dashboard/notifications
GET /api/institute/dashboard/quick-stats
```

**Dashboard Summary Response:**
```json
{
  "totalMentors": 45,
  "activeMentors": 38,
  "pendingMentorApplications": 8,
  "totalEvents": 12,
  "upcomingEvents": 3,
  "completedEvents": 7,
  "totalEventAttendees": 1250,
  "monthlyGrowth": {
    "mentors": 15.2,
    "events": 25.0,
    "eventAttendees": 18.5
  }
}
```

### **Advanced Analytics**
```http
GET /api/institute/analytics/mentor-performance
GET /api/institute/analytics/event-engagement
GET /api/institute/analytics/mentor-effectiveness
GET /api/institute/analytics/event-success-metrics
GET /api/institute/analytics/growth-metrics
POST /api/institute/analytics/custom-report
GET /api/institute/analytics/export/{report_id}
```

**Analytics Response Example:**
```json
{
  "mentorPerformance": [
    {
      "month": "2024-01",
      "activeMentors": 38,
      "newApplications": 12,
      "approvalRate": 75.0,
      "averageRating": 4.6
    }
  ],
  "eventStats": [
    {
      "eventId": "event-123",
      "title": "AI in Education Workshop",
      "attendees": 150,
      "attendanceRate": 95.2,
      "satisfactionScore": 4.8
    }
  ],
  "mentorEffectiveness": [
    {
      "mentorId": "mentor-456",
      "name": "Dr. Smith",
      "specialization": "Mathematics",
      "sessionsCompleted": 25,
      "averageRating": 4.8,
      "responseTime": "2.5 hours"
    }
  ]
}
```

---

## � **2. MENTOR MANAGEMENT**

### **Mentor Applications & Invitations**
```http
GET /api/institute/mentors/applications
POST /api/institute/mentors/applications/{app_id}/approve
POST /api/institute/mentors/applications/{app_id}/reject
POST /api/institute/mentors/invite
GET /api/institute/mentors/invitations
```

**Mentor Application Response:**
```json
{
  "data": [
    {
      "id": "mentor-123",
      "firstName": "Dr. Sarah",
      "lastName": "Williams",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "status": "active",
      "joinDate": "2023-10-15T00:00:00Z",
      "expertise": ["Mathematics", "Statistics"],
      "hourlyRate": 75.00,
      "hoursPerWeek": 20,
      "studentsAssigned": 15,
      "averageRating": 4.9,
      "completedSessions": 245
    }
  ],
  "applications": [
    {
      "id": "app-456",
      "applicantId": "user-789",
      "applicantName": "Dr. Michael Brown",
      "applicantEmail": "<EMAIL>",
      "status": "pending",
      "applicationDate": "2024-01-12T14:30:00Z",
      "applicationMessage": "I would like to join as a mathematics mentor...",
      "expertise": ["Advanced Mathematics", "Calculus"],
      "proposedHourlyRate": 80.00,
      "availabilityHours": 25
    }
  ]
}
```

### **Mentor Management**
```http
GET /api/institute/mentors
GET /api/institute/mentors/{mentor_id}
PUT /api/institute/mentors/{mentor_id}
POST /api/institute/mentors/{mentor_id}/activate
POST /api/institute/mentors/{mentor_id}/deactivate
GET /api/institute/mentors/{mentor_id}/performance
GET /api/institute/mentors/{mentor_id}/assignments
POST /api/institute/mentors/{mentor_id}/assign-competition
```

### **Mentor Analytics**
```http
GET /api/institute/mentors/performance-report
GET /api/institute/mentors/{mentor_id}/effectiveness-metrics
GET /api/institute/mentors/utilization-report
```

---

## 📅 **3. EVENT MANAGEMENT**

### **Event CRUD Operations**
```http
GET /api/institute/events
POST /api/institute/events
GET /api/institute/events/{event_id}
PUT /api/institute/events/{event_id}
DELETE /api/institute/events/{event_id}
POST /api/institute/events/{event_id}/publish
POST /api/institute/events/{event_id}/cancel
```

**Event Creation Request:**
```json
{
  "title": "Annual Science Fair 2024",
  "description": "Showcase of student science projects and innovations",
  "eventType": "academic",
  "categoryId": "cat-science",
  "startDateTime": "2024-03-15T09:00:00Z",
  "endDateTime": "2024-03-15T17:00:00Z",
  "location": {
    "venue": "Main Auditorium",
    "address": "123 Institute Street, City, State",
    "capacity": 500
  },
  "registrationRequired": true,
  "registrationDeadline": "2024-03-10T23:59:59Z",
  "isPublic": true,
  "targetAudience": ["students", "teachers", "parents"],
  "organizers": ["teacher-123", "teacher-456"],
  "budget": 10000.00,
  "expectedAttendees": 300
}
```

### **Event Management**
```http
GET /api/institute/events/{event_id}/attendees
POST /api/institute/events/{event_id}/register
DELETE /api/institute/events/{event_id}/attendees/{user_id}
POST /api/institute/events/{event_id}/send-reminder
GET /api/institute/events/{event_id}/check-in
POST /api/institute/events/{event_id}/check-in/{user_id}
```

### **Event Types & Categories**
```http
GET /api/institute/events/categories
POST /api/institute/events/categories
PUT /api/institute/events/categories/{category_id}
DELETE /api/institute/events/categories/{category_id}
GET /api/institute/events/templates
POST /api/institute/events/from-template/{template_id}
```

### **Event Analytics**
```http
GET /api/institute/events/{event_id}/analytics
GET /api/institute/events/{event_id}/attendance-report
GET /api/institute/events/success-metrics
POST /api/institute/events/{event_id}/feedback-survey
GET /api/institute/events/{event_id}/feedback-results
```

---

## 📊 **4. REPORTING & ANALYTICS**

### **Mentor Reports**
```http
GET /api/institute/reports/mentor-performance
GET /api/institute/reports/mentor-utilization
GET /api/institute/reports/mentor-applications-summary
GET /api/institute/reports/mentor-satisfaction
POST /api/institute/reports/custom-mentor-report
```

### **Event Reports**
```http
GET /api/institute/reports/event-performance
GET /api/institute/reports/event-attendance
GET /api/institute/reports/event-feedback-summary
GET /api/institute/reports/event-roi-analysis
POST /api/institute/reports/custom-event-report
```

### **Export & Scheduling**
```http
POST /api/institute/reports/export
GET /api/institute/reports/scheduled
POST /api/institute/reports/schedule
DELETE /api/institute/reports/scheduled/{schedule_id}
GET /api/institute/reports/export-history
```

---

## 💬 **5. COMMUNICATION & NOTIFICATIONS**

### **Mentor Communication**
```http
GET /api/institute/communications/mentor-messages
POST /api/institute/communications/send-to-mentor
GET /api/institute/communications/mentor-conversations
GET /api/institute/communications/mentor-conversations/{conversation_id}
POST /api/institute/communications/broadcast-to-mentors
```

### **Event Communication**
```http
GET /api/institute/communications/event-announcements
POST /api/institute/communications/event-announcement
PUT /api/institute/communications/event-announcements/{announcement_id}
DELETE /api/institute/communications/event-announcements/{announcement_id}
POST /api/institute/communications/event-announcements/{announcement_id}/publish
```

### **Notifications**
```http
GET /api/institute/notifications
POST /api/institute/notifications/send
GET /api/institute/notifications/templates
POST /api/institute/notifications/templates
PUT /api/institute/notifications/templates/{template_id}
GET /api/institute/notifications/delivery-status
```

---

## 🏢 **6. INSTITUTE SETTINGS & CONFIGURATION**

### **Institute Profile**
```http
GET /api/institute/profile
PUT /api/institute/profile
POST /api/institute/profile/logo
DELETE /api/institute/profile/logo
GET /api/institute/profile/verification-status
POST /api/institute/profile/verify
```

### **System Configuration**
```http
GET /api/institute/settings
PUT /api/institute/settings
GET /api/institute/settings/academic-calendar
PUT /api/institute/settings/academic-calendar
GET /api/institute/settings/grading-system
PUT /api/institute/settings/grading-system
```

### **User Roles & Permissions**
```http
GET /api/institute/roles
POST /api/institute/roles
PUT /api/institute/roles/{role_id}
DELETE /api/institute/roles/{role_id}
GET /api/institute/permissions
POST /api/institute/users/{user_id}/assign-role
DELETE /api/institute/users/{user_id}/roles/{role_id}
```

### **Integration Settings**
```http
GET /api/institute/integrations
POST /api/institute/integrations/configure
PUT /api/institute/integrations/{integration_id}
DELETE /api/institute/integrations/{integration_id}
POST /api/institute/integrations/{integration_id}/test
```

---

## � **API IMPLEMENTATION PRIORITY**

### **Phase 1 - Core Functionality (Immediate)**
1. Dashboard & Analytics
2. Mentor Management (Applications, Invitations, Performance)
3. Event Management (CRUD, Registration, Analytics)
4. Basic Communication & Notifications

### **Phase 2 - Enhanced Features (Short-term)**
1. Advanced Analytics (Mentor & Event Performance)
2. Reporting System (Mentor & Event Reports)
3. Institute Settings & Configuration
4. Enhanced Communication Features

### **Phase 3 - Advanced Features (Medium-term)**
1. Mobile App Support
2. Advanced Security & Audit
3. External Integrations
4. Workflow Automation

### **Phase 4 - Enterprise Features (Long-term)**
1. AI-Powered Insights for Mentor Matching
2. Predictive Analytics for Event Success
3. Advanced Workflow Management
4. Custom Integrations

---

## 🔧 **Technical Requirements**

### **Performance Standards**
- Response time: < 200ms for dashboard APIs
- Response time: < 500ms for mentor/event analytics
- Pagination: Support for mentor and event listings
- Caching: Redis for frequently accessed mentor/event data
- Rate limiting: Per endpoint and institute-based

### **Security Requirements**
- JWT-based authentication with institute-level permissions
- Role-based access control (RBAC) for institute operations
- API rate limiting for mentor and event operations
- Input validation for mentor applications and event data
- Audit logging for mentor approvals and event management
- Data encryption for sensitive mentor and event information

### **Scalability Requirements**
- Support for multiple mentors per institute
- Efficient event management for large attendee lists
- Background processing for mentor application workflows
- Real-time updates for event registrations and mentor status
- CDN support for event media and mentor profiles

---

# 📚 **EXISTING SUBSCRIPTION SYSTEM (LEGACY)**

## 📚 **Subscription System**

### **Get Available Plans**
```http
GET /api/subscriptions/plans
```

**Query Parameters:**
- `user_type` (optional): `student` | `teacher` | `institute` | `mentor`
- `is_active` (optional): `true` | `false` (default: `true`)
- `skip` (optional): Pagination offset (default: `0`)
- `limit` (optional): Items per page (default: `100`)

**Example Request:**
```http
GET /api/subscriptions/plans?user_type=teacher&is_active=true
```

**Response (200):**
```json
{
  "plans": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Teacher Basic",
      "description": "Essential features for individual teachers",
      "user_type": "teacher",
      "price": 29.99,
      "currency": "USD",
      "billing_cycle": "monthly",
      "features": [
        "Up to 5 classes",
        "Basic analytics",
        "Email support"
      ],
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 1,
  "skip": 0,
  "limit": 100
}
```

### **Subscribe to Plan**
```http
POST /api/subscriptions/subscribe
```

**Headers:**
```http
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "plan_id": "550e8400-e29b-41d4-a716-446655440000",
  "payment_method": "stripe",
  "billing_address": {
    "street": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "postal_code": "12345",
    "country": "US"
  }
}
```

**Response (201):**
```json
{
  "subscription": {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "user_id": "550e8400-e29b-41d4-a716-446655440002",
    "plan_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "active",
    "current_period_start": "2024-01-15T00:00:00Z",
    "current_period_end": "2024-02-15T00:00:00Z",
    "created_at": "2024-01-15T10:30:00Z"
  },
  "payment": {
    "id": "pay_1234567890",
    "amount": 29.99,
    "currency": "USD",
    "status": "succeeded"
  }
}
```

### **Get User Subscriptions**
```http
GET /api/subscriptions/my-subscriptions
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "subscriptions": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440001",
      "plan": {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "name": "Teacher Basic",
        "price": 29.99,
        "currency": "USD",
        "billing_cycle": "monthly"
      },
      "status": "active",
      "current_period_start": "2024-01-15T00:00:00Z",
      "current_period_end": "2024-02-15T00:00:00Z",
      "auto_renew": true,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### **Cancel Subscription**
```http
POST /api/subscriptions/{subscription_id}/cancel
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body (optional):**
```json
{
  "reason": "No longer needed",
  "cancel_at_period_end": true
}
```

**Response (200):**
```json
{
  "subscription": {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "status": "canceled",
    "canceled_at": "2024-01-15T15:30:00Z",
    "cancel_at_period_end": true,
    "current_period_end": "2024-02-15T00:00:00Z"
  }
}
```

---