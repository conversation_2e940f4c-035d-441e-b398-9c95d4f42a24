from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Institute.EventManagement import (
    create_institute_event, get_institute_events, get_institute_event,
    update_institute_event, delete_institute_event, publish_event,
    cancel_event, get_event_attendees
)

# Import Schemas
from Schemas.Institute.EventManagement import (
    InstituteEventCreate, InstituteEventOut, EventAttendeesResponse,
    EventRegistrationRequest, EventCheckInRequest, EventReminderRequest,
    EventCategoryCreate, EventCategoryOut, EventTemplateCreate, EventTemplateOut,
    EventAnalyticsOut, EventAttendanceReportOut, EventSuccessMetricsOut,
    EventFeedbackSurveyCreate, EventFeedbackResultsOut, InstituteEventsResponse,
    EventPublishRequest, EventCancelRequest
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# Event CRUD Operations
@router.get("", response_model=InstituteEventsResponse)
def get_institute_events_route(
    status: Optional[str] = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get events for institute"""
    current_user = get_current_user(token, db)
    return get_institute_events(db, current_user.id, status, skip, limit)


@router.post("", response_model=InstituteEventOut)
def create_event_route(
    event_data: InstituteEventCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Create event for institute"""
    current_user = get_current_user(token, db)
    return create_institute_event(db, current_user.id, event_data)


@router.get("/{event_id}", response_model=InstituteEventOut)
def get_event_route(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get specific event for institute"""
    current_user = get_current_user(token, db)
    return get_institute_event(db, current_user.id, event_id)


@router.put("/{event_id}", response_model=InstituteEventOut)
def update_event_route(
    event_id: UUID,
    event_data: InstituteEventCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Update event for institute"""
    current_user = get_current_user(token, db)
    return update_institute_event(db, current_user.id, event_id, event_data)


@router.delete("/{event_id}")
def delete_event_route(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Delete event for institute"""
    current_user = get_current_user(token, db)
    delete_institute_event(db, current_user.id, event_id)
    return {"message": "Event deleted successfully"}


@router.post("/{event_id}/publish", response_model=InstituteEventOut)
def publish_event_route(
    event_id: UUID,
    publish_data: EventPublishRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Publish event"""
    current_user = get_current_user(token, db)
    return publish_event(db, current_user.id, event_id, publish_data)


@router.post("/{event_id}/cancel", response_model=InstituteEventOut)
def cancel_event_route(
    event_id: UUID,
    cancel_data: EventCancelRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Cancel event"""
    current_user = get_current_user(token, db)
    return cancel_event(db, current_user.id, event_id, cancel_data)


# Event Management
@router.get("/{event_id}/attendees", response_model=EventAttendeesResponse)
def get_event_attendees_route(
    event_id: UUID,
    status: Optional[str] = Query(None, description="Filter by attendance status"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get attendees for event"""
    current_user = get_current_user(token, db)
    return get_event_attendees(db, current_user.id, event_id, status)


@router.post("/{event_id}/register")
def register_user_for_event(
    event_id: UUID,
    registration_data: EventRegistrationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Register user for event (institute can register users)"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.Events import Event, EventRegistration
    from datetime import datetime, timezone
    import uuid
    
    # Verify event belongs to institute
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == current_user.id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # Check if user is already registered
    existing_registration = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.user_id == registration_data.user_id
    ).first()
    
    if existing_registration:
        raise HTTPException(status_code=400, detail="User is already registered for this event")
    
    # Create registration
    registration = EventRegistration(
        id=uuid.uuid4(),
        event_id=event_id,
        user_id=registration_data.user_id,
        ticket_id=registration_data.ticket_id,
        quantity=registration_data.quantity,
        status="confirmed",  # Institute registrations are auto-confirmed
        attendee_info=registration_data.attendee_info,
        special_requirements=registration_data.special_requirements,
        emergency_contact=registration_data.emergency_contact,
        created_at=datetime.now(timezone.utc)
    )
    
    db.add(registration)
    db.commit()
    
    return {"message": "User registered successfully", "registration_id": str(registration.id)}


@router.delete("/{event_id}/attendees/{user_id}")
def remove_attendee_from_event(
    event_id: UUID,
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Remove attendee from event"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.Events import Event, EventRegistration
    
    # Verify event belongs to institute
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == current_user.id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # Find registration
    registration = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.user_id == user_id
    ).first()
    
    if not registration:
        raise HTTPException(status_code=404, detail="Registration not found")
    
    db.delete(registration)
    db.commit()
    
    return {"message": "Attendee removed successfully"}


@router.post("/{event_id}/send-reminder")
def send_event_reminder(
    event_id: UUID,
    reminder_data: EventReminderRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Send reminder to event attendees"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.Events import Event, EventRegistration
    
    # Verify event belongs to institute
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == current_user.id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # Get attendees based on send_to filter
    query = db.query(EventRegistration).filter(EventRegistration.event_id == event_id)
    
    if reminder_data.send_to == "confirmed":
        query = query.filter(EventRegistration.status == "confirmed")
    elif reminder_data.send_to == "pending":
        query = query.filter(EventRegistration.status == "pending")
    
    registrations = query.all()
    
    # TODO: Implement actual reminder sending logic
    # For now, just return success message
    
    return {
        "message": f"Reminder sent to {len(registrations)} attendees",
        "sent_count": len(registrations),
        "channels": reminder_data.send_via
    }


@router.get("/{event_id}/check-in")
def get_checkin_status(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get check-in status for event"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.Events import Event, EventRegistration
    
    # Verify event belongs to institute
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == current_user.id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # Get registration statistics
    total_registered = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.status == "confirmed"
    ).count()
    
    # Placeholder for check-in statistics
    checked_in = 0  # Would need check-in system
    
    return {
        "event_id": str(event_id),
        "total_registered": total_registered,
        "checked_in": checked_in,
        "check_in_rate": (checked_in / max(total_registered, 1)) * 100,
        "event_status": event.status.value
    }


@router.post("/{event_id}/check-in/{user_id}")
def check_in_attendee(
    event_id: UUID,
    user_id: UUID,
    checkin_data: EventCheckInRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Check in attendee for event"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.Events import Event, EventRegistration
    from datetime import datetime, timezone
    
    # Verify event belongs to institute
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == current_user.id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # Find registration
    registration = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.user_id == user_id,
        EventRegistration.status == "confirmed"
    ).first()
    
    if not registration:
        raise HTTPException(status_code=404, detail="Registration not found or not confirmed")
    
    # TODO: Implement actual check-in logic
    # For now, just return success message
    
    checkin_time = checkin_data.check_in_time or datetime.now(timezone.utc)
    
    return {
        "message": "Attendee checked in successfully",
        "user_id": str(user_id),
        "check_in_time": checkin_time,
        "notes": checkin_data.notes
    }
